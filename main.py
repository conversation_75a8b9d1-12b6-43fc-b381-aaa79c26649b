#!/usr/bin/env python3
"""
163邮箱转发服务主程序
"""

import sys
import argparse
import time
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config_manager import config_manager
from logger_config import setup_global_logging, get_logger
from email_service import email_service


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='163邮箱转发服务')
    
    parser.add_argument(
        '--config', '-c',
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    
    parser.add_argument(
        '--test', '-t',
        action='store_true',
        help='发送测试邮件并退出'
    )
    
    parser.add_argument(
        '--status', '-s',
        action='store_true',
        help='显示服务状态'
    )
    
    parser.add_argument(
        '--daemon', '-d',
        action='store_true',
        help='以守护进程模式运行'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='设置日志级别'
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()
    
    try:
        # 设置配置文件路径
        config_manager.config_file = args.config
        
        # 加载配置
        config = config_manager.load_config()
        
        # 设置日志系统
        setup_global_logging(config.logging)
        logger = get_logger(__name__)
        
        # 如果指定了日志级别，覆盖配置文件中的设置
        if args.log_level:
            from logger_config import logger_manager
            logger_manager.set_level(args.log_level)
        
        logger.info("=== 163邮箱转发服务启动 ===")
        logger.info(f"配置文件: {args.config}")
        logger.info(f"163邮箱: {config.email_163.username}")
        logger.info(f"转发目标: {', '.join(config.forward.target_emails)}")
        
        # 处理不同的运行模式
        if args.test:
            # 测试模式
            logger.info("运行模式: 测试邮件发送")
            success = email_service.send_test_email()
            if success:
                logger.info("测试邮件发送成功")
                return 0
            else:
                logger.error("测试邮件发送失败")
                return 1
        
        elif args.status:
            # 状态查看模式
            logger.info("运行模式: 状态查看")
            status = email_service.get_status()
            print("\n=== 服务状态 ===")
            for key, value in status.items():
                print(f"{key}: {value}")
            return 0
        
        else:
            # 正常服务模式
            logger.info("运行模式: 邮件转发服务")
            
            if args.daemon:
                logger.info("以守护进程模式运行")
                # 在实际部署中，这里可以添加守护进程化的代码
            
            # 启动服务
            if not email_service.start():
                logger.error("服务启动失败")
                return 1
            
            try:
                # 保持服务运行
                logger.info("服务正在运行，按 Ctrl+C 停止...")
                while email_service.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("收到停止信号")
            finally:
                email_service.stop()
            
            logger.info("=== 163邮箱转发服务已停止 ===")
            return 0
    
    except FileNotFoundError as e:
        print(f"错误: {e}")
        print("请确保配置文件存在并且路径正确")
        return 1
    
    except ValueError as e:
        print(f"配置错误: {e}")
        print("请检查配置文件格式和内容")
        return 1
    
    except Exception as e:
        print(f"未知错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
